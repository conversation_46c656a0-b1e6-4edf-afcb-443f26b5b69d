#!/usr/bin/env python3
"""
Link Click Prediction AI Agent - Gradio Web Interface
"""

import gradio as gr
import requests
import json
import asyncio
import aiohttp
import re
from typing import Optional, Dict, Any, List
import logging
from datetime import datetime

# Logging setup
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configuration
API_BASE_URL = "http://localhost:8000"
RAGFLOW_BASE_URL = "http://localhost:9380"

class LinkAnalysisInterface:
    def __init__(self):
        self.session = None
        
    async def get_session(self):
        """Get or create aiohttp session"""
        if self.session is None:
            self.session = aiohttp.ClientSession()
        return self.session
    
    async def close_session(self):
        """Close aiohttp session"""
        if self.session:
            await self.session.close()
            self.session = None
    
    def analyze_link_clicks(self, website_url: str) -> tuple:
        """Analyze website links for click prediction"""
        try:
            if not website_url.strip():
                return "❌ Lütfen bir website URL'si girin.", ""

            # Add protocol if missing
            if not website_url.startswith(('http://', 'https://')):
                website_url = 'https://' + website_url

            # Prepare the query for link analysis
            query_data = {
                "query": f"Bu web sitesindeki hangi linklerin tıklanma olasılığı yüksek? Analiz et: {website_url}",
                "context": f"Website URL: {website_url}",
                "stream": False
            }

            # Make request to the agent (10 minute timeout for RAG processing)
            response = requests.post(
                f"{API_BASE_URL}/query",
                json=query_data,
                timeout=600  # 10 minutes for RAG-powered analysis
            )

            if response.status_code == 200:
                result = response.json()
                raw_response = result.get("response", "Yanıt alınamadı.")

                # Try to parse JSON response
                formatted_result = self.parse_and_format_response(raw_response)
                return formatted_result, raw_response
            else:
                error_msg = f"❌ API Hatası: {response.status_code} - {response.text}"
                return error_msg, ""

        except requests.exceptions.RequestException as e:
            logger.error(f"API request error: {e}")
            error_msg = f"❌ Bağlantı hatası: {str(e)}"
            return error_msg, ""
        except Exception as e:
            logger.error(f"Unexpected error: {e}")
            error_msg = f"❌ Beklenmeyen hata: {str(e)}"
            return error_msg, ""

    def parse_and_format_response(self, response: str) -> str:
        """Parse and format the response nicely"""
        try:
            # First, try to find JSON code blocks (```json ... ```)
            json_pattern = r'```json\s*\n(.*?)\n```'
            json_match = re.search(json_pattern, response, re.DOTALL)

            if json_match:
                json_str = json_match.group(1)
                try:
                    data = json.loads(json_str)
                    return self.format_structured_data(data)
                except json.JSONDecodeError:
                    pass

            # Try to find JSON objects directly in the response
            start_idx = response.find('{')
            end_idx = response.rfind('}') + 1

            if start_idx != -1 and end_idx != 0:
                json_str = response[start_idx:end_idx]
                try:
                    data = json.loads(json_str)
                    return self.format_structured_data(data)
                except json.JSONDecodeError:
                    pass

            # If no JSON found, format as plain text analysis
            return self.format_plain_text_response(response)

        except Exception as e:
            logger.error(f"Error parsing response: {e}")
            return self.format_plain_text_response(response)

    def format_structured_data(self, data: dict) -> str:
        """Format structured JSON data"""
        formatted = "# Link Click Analysis Results\n\n"

        if 'analysis_target' in data:
            formatted += f"**Analyzed Site:** {data['analysis_target']}\n\n"

        if 'link_predictions' in data:
            # Sort links by probability (highest first)
            sorted_links = sorted(
                data['link_predictions'],
                key=lambda x: float(x.get('predicted_click_probability', 0)),
                reverse=True
            )

            formatted += "## Link Analysis (Ordered by Probability)\n\n"

            for i, item in enumerate(sorted_links, 1):
                prob = item.get('predicted_click_probability', 'N/A')
                link_text = item.get('link_text', 'N/A')
                link_url = item.get('link_url', '')

                # Determine category
                try:
                    prob_float = float(prob)
                    if prob_float >= 0.8:
                        category = "High"
                    elif prob_float >= 0.4:
                        category = "Medium"
                    else:
                        category = "Low"
                except (ValueError, TypeError):
                    category = "Unknown"

                formatted += f"### {i}. {link_text}\n"
                if link_url:
                    formatted += f"**Link URL:** {link_url}\n"
                formatted += f"**Click Probability:** {prob} ({category})\n\n"

                if 'analysis' in item:
                    analysis = item['analysis']
                    if 'visual_hierarchy' in analysis:
                        formatted += f"**Visual Hierarchy:** {analysis['visual_hierarchy']}\n\n"
                    if 'design_pattern' in analysis:
                        formatted += f"**Design Pattern:** {analysis['design_pattern']}\n\n"
                    if 'psychological_triggers' in analysis:
                        if isinstance(analysis['psychological_triggers'], dict):
                            formatted += f"**Psychological Triggers:**\n"
                            for key, value in analysis['psychological_triggers'].items():
                                formatted += f"- {key.replace('_', ' ').title()}: {value}\n"
                        else:
                            formatted += f"**Psychological Triggers:** {analysis['psychological_triggers']}\n"
                        formatted += "\n"

                formatted += "---\n\n"

        # Handle legacy structure
        elif 'high_click_probability' in data:
            formatted += "### High Click Probability\n"
            for item in data['high_click_probability']:
                formatted += f"• **{item.get('text', 'N/A')}** - `{item.get('url', 'N/A')}`\n"
                formatted += f"  Probability: {item.get('probability', 'N/A')}\n"
                formatted += f"  Reason: {item.get('reason', 'N/A')}\n\n"

        # Add summary if available
        if 'summary' in data:
            formatted += f"## Overall Assessment\n{data['summary']}\n"

        # If no recognized structure, show the raw JSON nicely
        if formatted == "# Link Click Analysis Results\n\n":
            formatted += "### Analysis Details\n"
            formatted += f"```json\n{json.dumps(data, indent=2, ensure_ascii=False)}\n```\n"

        return formatted

    def format_plain_text_response(self, response: str) -> str:
        """Format plain text response"""
        formatted = "## 📄 Link Analizi Sonucu\n\n"

        # Split into paragraphs and format nicely
        paragraphs = response.split('\n\n')
        for para in paragraphs:
            if para.strip():
                # Check if it looks like a heading
                if any(keyword in para.lower() for keyword in ['analiz', 'sonuç', 'link', 'tıklama', 'olasılık']):
                    formatted += f"### {para.strip()}\n\n"
                else:
                    formatted += f"{para.strip()}\n\n"

        return formatted
    
    def check_health(self) -> str:
        """Check agent health status"""
        try:
            response = requests.get(f"{API_BASE_URL}/health", timeout=30)
            if response.status_code == 200:
                data = response.json()
                # Get provider info
                provider_info = self.get_provider_info()
                return f"✅ System Healthy\n📊 Status: {data.get('status', 'Unknown')}\n⏰ Time: {data.get('timestamp', 'Unknown')}\n{provider_info}"
            else:
                return f"❌ System Error: {response.status_code}"
        except Exception as e:
            return f"❌ Connection Error: {str(e)}"

    def get_provider_info(self) -> str:
        """Get LLM provider information"""
        try:
            # Load config to get provider info
            import yaml
            with open('config.yaml', 'r') as f:
                config = yaml.safe_load(f)

            active_provider = config.get('active_provider', 'openrouter')
            if active_provider == 'openrouter':
                model = config.get('openrouter', {}).get('model', 'Unknown')
                return f"🤖 Provider: OpenRouter\n📋 Model: {model}"
            elif active_provider == 'ollama':
                model = config.get('ollama', {}).get('model', 'Unknown')
                return f"🤖 Provider: Ollama (Local)\n📋 Model: {model}"
            else:
                return f"🤖 Provider: {active_provider}"
        except Exception as e:
            return f"🤖 Provider: Unknown (Error: {str(e)})"
    
    def get_stats(self) -> str:
        """Get agent statistics"""
        try:
            response = requests.get(f"{API_BASE_URL}/stats", timeout=30)
            if response.status_code == 200:
                data = response.json()
                stats_text = "📈 **Sistem İstatistikleri**\n\n"
                for key, value in data.items():
                    stats_text += f"• **{key}**: {value}\n"
                return stats_text
            else:
                return f"❌ İstatistik alınamadı: {response.status_code}"
        except Exception as e:
            return f"❌ İstatistik hatası: {str(e)}"

# Initialize interface
interface = LinkAnalysisInterface()

def analyze_website(website_url: str) -> tuple:
    """Analyze website for link click predictions"""
    if not website_url.strip():
        return "❌ Lütfen bir website URL'si girin.", ""

    # Get analysis from agent
    formatted_result, raw_response = interface.analyze_link_clicks(website_url)

    return formatted_result, raw_response

def clear_results():
    """Clear analysis results"""
    return "", "", ""

def load_example_websites():
    """Load example website URLs"""
    examples = [
        "https://www.example.com",
        "https://news.ycombinator.com",
        "https://www.reddit.com",
        "https://github.com",
        "https://stackoverflow.com"
    ]
    return examples

# Create Gradio interface
def create_interface():
    """Create the main Gradio interface"""

    with gr.Blocks(
        title="🔗 Link Click Prediction AI",
        theme=gr.themes.Soft(),
        css="""
        .gradio-container {
            max-width: 1400px !important;
        }
        .analysis-result {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        .url-input {
            font-family: monospace;
        }
        """
    ) as demo:

        # Header
        gr.Markdown(
            """
            # Link Click Prediction AI
            ### Website Link Analysis and Click Probability Prediction

            This tool analyzes links on the website you provide and predicts which links
            are most likely to be clicked by users.
            """
        )

        with gr.Row():
            # Left column - Input and controls
            with gr.Column(scale=1):
                gr.Markdown("## Website Analysis")

                # Website URL input
                website_input = gr.Textbox(
                    label="Website URL",
                    placeholder="https://example.com",
                    lines=1,
                    elem_classes=["url-input"]
                )

                # Analyze button
                analyze_btn = gr.Button("Analyze", variant="primary", size="lg")

                # Clear button
                clear_btn = gr.Button("Clear", variant="secondary")

                # Example websites
                gr.Markdown("### Example Websites")
                example_websites = gr.Examples(
                    examples=load_example_websites(),
                    inputs=website_input,
                    label="Try clicking example websites:"
                )

                # System status
                gr.Markdown("### System Status")
                health_btn = gr.Button("System Check")
                health_output = gr.Textbox(
                    label="System Status",
                    lines=3,
                    interactive=False
                )

                stats_btn = gr.Button("Statistics")
                stats_output = gr.Markdown(
                    label="System Statistics",
                    value="Click button to view statistics..."
                )

            # Right column - Results
            with gr.Column(scale=2):
                gr.Markdown("## Analysis Results")

                # Analysis results
                analysis_output = gr.Markdown(
                    label="Link Click Analysis",
                    value="Analysis results will appear here...",
                    elem_classes=["analysis-result"]
                )

                # Raw response (collapsible)
                with gr.Accordion("Raw Response (For Developers)", open=False):
                    raw_output = gr.Textbox(
                        label="Ham API Yanıtı",
                        lines=10,
                        interactive=False,
                        show_copy_button=True
                    )

                # Usage info
                gr.Markdown(
                    """
                    **💡 Nasıl Kullanılır:**
                    1. Sol panele analiz etmek istediğiniz website URL'sini girin
                    2. "Analiz Et" butonuna tıklayın
                    3. AI, sayfadaki linkleri analiz ederek tıklama olasılıklarını hesaplar
                    4. Sonuçlar kategorilere ayrılarak gösterilir:
                       - ✅ **Yüksek Olasılık**: Çok tıklanması muhtemel linkler
                       - ⚠️ **Orta Olasılık**: Orta düzeyde tıklanması muhtemel linkler
                       - ❌ **Düşük Olasılık**: Az tıklanması muhtemel linkler

                    **🎯 Analiz Kriterleri:**
                    - Link metni ve konumu
                    - Görsel önem derecesi
                    - Kullanıcı deneyimi faktörleri
                    - İçerik relevansı
                    """
                )
        
        # Event handlers
        analyze_btn.click(
            fn=analyze_website,
            inputs=[website_input],
            outputs=[analysis_output, raw_output]
        )

        website_input.submit(
            fn=analyze_website,
            inputs=[website_input],
            outputs=[analysis_output, raw_output]
        )

        clear_btn.click(
            fn=clear_results,
            outputs=[analysis_output, raw_output, website_input]
        )

        health_btn.click(
            fn=interface.check_health,
            outputs=health_output
        )

        stats_btn.click(
            fn=interface.get_stats,
            outputs=stats_output
        )
    
    return demo

if __name__ == "__main__":
    # Create and launch interface
    demo = create_interface()
    
    # Launch with custom settings
    demo.launch(
        server_name="0.0.0.0",
        server_port=7869,
        share=False,
        debug=True,
        show_error=True,
        quiet=False
    )
