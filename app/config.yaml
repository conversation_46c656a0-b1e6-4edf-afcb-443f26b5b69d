api_key: "AIzaSyDn5wXxNJ5uMlZZR2zgu_MH5zEvwLer-xY"
number_of_links: 30

# RAGFlow Settings
ragflow_api_key: "ragflow-U1NTVhMjU0NmM1NjExZjBiMGY3ODJkNT"
ragflow_base_url: "http://ragflow-server:9380"
ragflow_dataset_name: "link_prefetch_pages"

# LLM Provider Configuration
# Set active_provider to "openrouter" or "ollama"
active_provider: "openrouter"

openrouter:
  api_key: "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"
  base_url: "https://openrouter.ai/api/v1"
  model: "google/gemini-2.5-pro"

ollama:
  base_url: "http://localhost:11434"
  model: "qwen3:30b-thinking-8Q-64K"
  # Alternative models: "llama3.1:70b", "mistral:7b", "codellama:13b"