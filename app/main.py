#!/usr/bin/env python3
"""
Link Analysis AI Agent - Main Application
Bu dosya agent'ı başlatır ve FastAPI web servisi sağlar.
"""

import asyncio
import logging
import os
import sys
import yaml
from pathlib import Path
from typing import Dict, Any, Optional
import uvicorn
from fastapi import FastAP<PERSON>, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel
import requests
import time
import threading

# Add current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agent import LinkAnalysisAgent
from gradio_interface import create_interface

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/main.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# FastAPI app
app = FastAPI(
    title="Link Analysis AI Agent",
    description="AI-powered link analysis and click prediction agent with RAGFlow integration",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global variables
config: Dict[str, Any] = {}
agent: Optional[LinkAnalysisAgent] = None

# Pydantic models
class QueryRequest(BaseModel):
    query: str
    max_links: Optional[int] = None
    use_ragflow: Optional[bool] = True

class QueryResponse(BaseModel):
    success: bool
    response: str
    sources: list = []
    error: Optional[str] = None

def load_config() -> Dict[str, Any]:
    """Load configuration from config.yaml"""
    config_path = Path("config.yaml")
    if not config_path.exists():
        logger.error("config.yaml not found!")
        raise FileNotFoundError("config.yaml not found!")
    
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    logger.info("Configuration loaded successfully")
    return config

def wait_for_ragflow(base_url: str, timeout: int = 300) -> bool:
    """Wait for RAGFlow to be ready"""
    logger.info(f"Waiting for RAGFlow at {base_url}...")
    start_time = time.time()
    
    while time.time() - start_time < timeout:
        try:
            response = requests.get(f"{base_url}/v1/api/health", timeout=10)
            if response.status_code == 200:
                logger.info("RAGFlow is ready!")
                return True
        except requests.exceptions.RequestException as e:
            logger.debug(f"RAGFlow not ready yet: {e}")
        
        time.sleep(10)
    
    logger.error(f"RAGFlow not ready after {timeout} seconds")
    return False

def start_gradio_interface():
    """Start Gradio interface in a separate thread"""
    try:
        logger.info("Starting Gradio interface...")
        demo = create_interface()
        demo.launch(
            server_name="0.0.0.0",
            server_port=7869,
            share=False,
            debug=True,
            show_error=True,
            quiet=False
        )
    except Exception as e:
        logger.error(f"Failed to start Gradio interface: {e}")

async def initialize_agent():
    """Initialize the agent"""
    global agent, config

    try:
        logger.info("Initializing Link Analysis Agent...")

        # Load configuration
        config = load_config()

        # Wait for RAGFlow to be ready
        ragflow_url = config.get('ragflow_base_url', 'http://ragflow-server:9380')
        if not wait_for_ragflow(ragflow_url):
            logger.warning("RAGFlow is not ready, agent will work without RAGFlow")

        # Initialize agent
        agent = LinkAnalysisAgent(config)
        await agent.initialize()

        logger.info("Agent initialized successfully!")

        # Start Gradio interface in a separate thread
        gradio_thread = threading.Thread(target=start_gradio_interface, daemon=True)
        gradio_thread.start()
        logger.info("Gradio interface started in background thread")

    except Exception as e:
        logger.error(f"Failed to initialize agent: {e}")
        raise

@app.on_event("startup")
async def startup_event():
    """Startup event handler"""
    await initialize_agent()

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Link Analysis AI Agent is running!",
        "status": "active",
        "agent_ready": agent is not None
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "agent_ready": agent is not None,
        "timestamp": time.time()
    }

@app.get("/config")
async def get_config():
    """Get current configuration (without sensitive data)"""
    safe_config = config.copy()
    # Remove sensitive information
    if 'api_key' in safe_config:
        safe_config['api_key'] = "***"
    if 'ragflow_api_key' in safe_config:
        safe_config['ragflow_api_key'] = "***"
    if 'openrouter' in safe_config and 'api_key' in safe_config['openrouter']:
        safe_config['openrouter']['api_key'] = "***"
    
    return safe_config

@app.post("/query", response_model=QueryResponse)
async def process_query(request: QueryRequest):
    """Process a query through the agent"""
    if not agent:
        raise HTTPException(status_code=503, detail="Agent not initialized")
    
    try:
        logger.info(f"Processing query: {request.query[:100]}...")
        
        # Process query through agent
        response = await agent.process_query(
            query=request.query,
            max_links=request.max_links or config.get('number_of_links', 30),
            use_ragflow=request.use_ragflow
        )
        
        return QueryResponse(
            success=True,
            response=response.get('response', ''),
            sources=response.get('sources', [])
        )
        
    except Exception as e:
        logger.error(f"Error processing query: {e}")
        return QueryResponse(
            success=False,
            response="",
            error=str(e)
        )

@app.post("/query/async")
async def process_query_async(request: QueryRequest, background_tasks: BackgroundTasks):
    """Process a query asynchronously"""
    if not agent:
        raise HTTPException(status_code=503, detail="Agent not initialized")
    
    # Generate task ID
    task_id = f"task_{int(time.time())}"
    
    # Add background task
    background_tasks.add_task(
        agent.process_query_background,
        task_id,
        request.query,
        request.max_links or config.get('number_of_links', 30),
        request.use_ragflow
    )
    
    return {"task_id": task_id, "status": "processing"}

@app.get("/task/{task_id}")
async def get_task_status(task_id: str):
    """Get task status"""
    if not agent:
        raise HTTPException(status_code=503, detail="Agent not initialized")
    
    status = agent.get_task_status(task_id)
    if not status:
        raise HTTPException(status_code=404, detail="Task not found")
    
    return status

@app.post("/ragflow/recreate-dataset")
async def recreate_ragflow_dataset():
    """Recreate RAGFlow dataset"""
    if not agent:
        raise HTTPException(status_code=503, detail="Agent not initialized")
    
    try:
        result = await agent.recreate_ragflow_dataset()
        return {"success": True, "result": result}
    except Exception as e:
        logger.error(f"Error recreating dataset: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/stats")
async def get_stats():
    """Get agent statistics"""
    if not agent:
        raise HTTPException(status_code=503, detail="Agent not initialized")
    
    return agent.get_stats()

if __name__ == "__main__":
    # Create logs directory
    os.makedirs("logs", exist_ok=True)
    
    # Run the application
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=False,
        log_level="info"
    )
