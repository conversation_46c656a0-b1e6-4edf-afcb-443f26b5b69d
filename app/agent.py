#!/usr/bin/env python3
"""
Agent-related classes for the Link Prefetching Judge, now powered by RAGFlow.
"""

import asyncio
import hashlib
import json
import urllib3
import warnings
import logging
import time
import os
import re
from typing import List, Dict, Any, Optional
from urllib.parse import urljoin, urlparse
from dataclasses import dataclass, field
import yaml

import requests
from bs4 import BeautifulSoup
from pydantic import BaseModel, Field
import cssutils

from agno.agent import Agent
from agno.tools import tool
from agno.models.openai import OpenAIChat
from ragflow_sdk import RAGFlow

# Suppress SSL warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
warnings.filterwarnings("ignore")
cssutils.log.setLevel(logging.FATAL)


def load_config(config_path: str = "config.yaml") -> Dict[str, Any]:
    """Load the configuration from a YAML file."""
    try:
        with open(config_path, 'r') as stream:
            return yaml.safe_load(stream) or {}
    except (FileNotFoundError, yaml.YAMLError):
        return {}


@dataclass
class LinkInfo:
    """Information about a link found in HTML, now with CSS data."""
    url: str
    text: str
    title: str
    rel: str
    position: int = 0
    location: str = "unknown"
    button_type: str = "none"
    css_data: Dict[str, Any] = field(default_factory=dict)


class ScoredLink(BaseModel):
    """Represents a single link with its selection score and reasoning."""
    url: str = Field(description="The exact URL of the link.")
    score: float = Field(description="The likelihood score (0.0 to 1.0) of the user clicking this link next.")
    reason: str = Field(description="A brief reason for assigning this specific score.")


class LinkSelection(BaseModel):
    """Structured output for link selection with individual scores."""
    selected_links: List[ScoredLink] = Field(
        description="List of selected link objects, each with a URL, score, and reason."
    )
    overall_reasoning: str = Field(
        description="Detailed overall reasoning for why this set of links was selected.",
        alias="reasoning"
    )
    consistency_hash: str = Field(
        description="Hash to ensure consistency across runs"
    )
    confidence_score: float = Field(
        description="Overall confidence score for the entire selection (0.0 to 1.0)"
    )


class LinkAnalysisAgent:
    """Link Analysis AI Agent - Enhanced with RAGFlow integration."""

    def __init__(self, config: Dict[str, Any]):
        """Initialize the agent with provided configuration."""
        self.config = config
        self.task_results = {}  # Store background task results
        self.stats = {
            'queries_processed': 0,
            'ragflow_queries': 0,
            'direct_queries': 0,
            'errors': 0
        }

        # Initialize components will be done in initialize() method
        self.model = None
        self.ragflow_client = None
        self.dataset = None
        self.agent = None
        self.retrieve_rag_context_tool = None
        self.num_links_to_select = config.get("number_of_links", 3)
        self.ragflow_dataset_name = config.get("ragflow_dataset_name")

    def _initialize_openrouter(self):
        """Initialize OpenRouter model"""
        openrouter_config = self.config.get("openrouter", {})
        or_api_key = openrouter_config.get("api_key")
        or_base_url = openrouter_config.get("base_url")
        or_model_name = openrouter_config.get("model")

        if not or_api_key or not or_base_url or not or_model_name:
            raise ValueError("OpenRouter configuration (api_key, base_url, model) not found in config.yaml.")

        self.model = OpenAIChat(
            id=or_model_name,
            api_key=or_api_key,
            base_url=or_base_url
        )

    def _initialize_ollama(self):
        """Initialize Ollama model"""
        ollama_config = self.config.get("ollama", {})
        ollama_base_url = ollama_config.get("base_url", "http://localhost:11434")
        ollama_model_name = ollama_config.get("model")

        if not ollama_model_name:
            raise ValueError("Ollama model name not found in config.yaml.")

        self.model = OpenAIChat(
            id=ollama_model_name,
            api_key="ollama",  # Ollama doesn't need real API key
            base_url=ollama_base_url
        )

    async def initialize(self):
        """Initialize the agent components asynchronously."""
        try:
            # Initialize LLM model based on active provider
            active_provider = self.config.get("active_provider", "openrouter")

            if active_provider == "openrouter":
                self._initialize_openrouter()
            elif active_provider == "ollama":
                self._initialize_ollama()
            else:
                raise ValueError(f"Unknown provider: {active_provider}. Use 'openrouter' or 'ollama'")

            print(f"[INIT] ✅ LLM Provider: {active_provider}")
            print(f"[INIT] ✅ Model: {self.model.id}")

            # Initialize RAGFlow client and dataset
            ragflow_api_key = self.config.get("ragflow_api_key")
            ragflow_base_url = self.config.get("ragflow_base_url")

            if ragflow_api_key and ragflow_api_key != "YOUR_RAGFLOW_API_KEY_HERE":
                try:
                    self.ragflow_client = RAGFlow(ragflow_api_key, ragflow_base_url)
                    datasets = self.ragflow_client.list_datasets(name=self.ragflow_dataset_name)
                    if datasets:
                        self.dataset = datasets[0]
                        print(f"[INIT] ✅ RAGFlow dataset '{self.ragflow_dataset_name}' found")
                    else:
                        print(f"[INIT] ⚠️ Dataset '{self.ragflow_dataset_name}' not found, will work without RAGFlow")
                except Exception as e:
                    print(f"[INIT] ⚠️ RAGFlow initialization failed: {e}, will work without RAGFlow")

            # Create the RAG retrieval tool if RAGFlow is available
            if self.ragflow_client and self.dataset:
                print("[INIT] Creating RAG tool...")
                self.retrieve_rag_context_tool = self._create_rag_tool()
                print(f"[INIT] ✅ RAG tool created successfully")

            # Initialize the agent
            self.agent = Agent(
                model=self.model,
                name="Link Analysis AI Agent",
                description=f"An intelligent AI agent that analyzes website links and predicts click probability.",
                goal="To analyze website links and predict which ones are most likely to be clicked by users.",
                instructions=[
                    "🚨 CRITICAL: You are a Link Analysis AI Agent. Before doing ANYTHING else, you MUST use the retrieve_rag_context tool to get relevant knowledge.",
                    "🚨 MANDATORY FIRST STEP: Call retrieve_rag_context with questions about link analysis, user behavior, and click prediction principles.",
                    "� FALLBACK RULE: If the RAG tool returns 'Based on general web usability principles', you should continue with your analysis using general UX knowledge.",
                    "Your ONLY task is to analyze website links and predict click probability using knowledge from your RAG system OR general UX principles if RAG fails.",
                    "When given a website URL, you must:",
                    "1. FIRST: Use retrieve_rag_context to ask about link analysis principles",
                    "2. SECOND: Use retrieve_rag_context to ask about user click behavior patterns",
                    "3. THIRD: Use retrieve_rag_context to ask about website design and UX principles",
                    "4. IF RAG FAILS: Continue with general web usability knowledge (visual hierarchy, user psychology, design patterns)",
                    "5. THEN: Analyze ALL INDIVIDUAL LINKS on the page using the retrieved knowledge OR general principles",
                    f"PROVIDE AT LEAST {self.num_links_to_select} INDIVIDUAL LINKS with specific link text and URLs when possible.",
                    "MANDATORY JSON FORMAT - respond ONLY with this structure:",
                    "```json",
                    "{",
                    "  \"analysis_target\": \"website_url\",",
                    "  \"link_predictions\": [",
                    "    {",
                    "      \"link_text\": \"Specific Link Text or Description\",",
                    "      \"link_url\": \"actual_link_url_if_available\",",
                    "      \"predicted_click_probability\": 0.85,",
                    "      \"analysis\": {",
                    "        \"visual_hierarchy\": \"Detailed position and visibility analysis based on RAG knowledge\",",
                    "        \"design_pattern\": \"Specific UI pattern type from knowledge base\",",
                    "        \"psychological_triggers\": \"User psychology factors from retrieved context\"",
                    "      }",
                    "    }",
                    "  ],",
                    "  \"summary\": \"Brief analysis summary incorporating RAG insights\"",
                    "}",
                    "```",
                    "Probability ranges: 0.8-1.0 = High, 0.4-0.7 = Medium, 0.0-0.3 = Low",
                    "Analyze INDIVIDUAL links like: specific article headlines, menu items, buttons, social media links, footer links, sidebar links, etc.",
                    "DO NOT group links - analyze each one separately with its own probability.",
                    "ALWAYS respond in English, even if the user query is in Turkish or another language.",
                    "🚨 REMEMBER: You MUST use retrieve_rag_context tool multiple times, but if it fails, continue with general UX knowledge!"
                ],
                tools=[self.retrieve_rag_context_tool] if self.retrieve_rag_context_tool else [],
                show_tool_calls=True,
                debug_mode=True
            )

            print(f"[INIT] ✅ Agent created successfully")
            print(f"[INIT] RAGFlow available: {self.ragflow_client is not None}")
            print("[INIT] Agent is ready!")

        except Exception as e:
            print(f"[INIT] ❌ Agent initialization failed: {e}")
            raise

    async def process_query(self, query: str, max_links: int = 30, use_ragflow: bool = True) -> Dict[str, Any]:
        """Process a user query and return a response - ALWAYS uses RAG tool."""
        try:
            self.stats['queries_processed'] += 1

            # FORCE RAG usage - never allow direct processing
            if self.ragflow_client and self.dataset and self.retrieve_rag_context_tool:
                self.stats['ragflow_queries'] += 1
                print("🚨 [FORCE RAG] Processing with mandatory RAG tool usage")
                response = await self._process_with_mandatory_rag(query)
            else:
                print("❌ [ERROR] RAGFlow not available - cannot process query without RAG")
                raise ValueError("RAGFlow is required but not available. Please check RAGFlow configuration.")

            return {
                'response': response,
                'sources': [],
                'method': 'ragflow_mandatory'
            }

        except Exception as e:
            self.stats['errors'] += 1
            print(f"[ERROR] Query processing failed: {e}")
            raise

    async def _process_with_mandatory_rag(self, query: str) -> str:
        """Process query with MANDATORY RAG tool usage - agent MUST use retrieve_rag_context tool."""
        try:
            print("🚨 [MANDATORY RAG] Starting processing with forced RAG tool usage")

            # Create a query that FORCES the agent to use RAG tool
            mandatory_rag_query = f"""
🚨 CRITICAL INSTRUCTION: You MUST use the retrieve_rag_context tool before providing any analysis.

Your task: {query}

MANDATORY STEPS:
1. FIRST: Call retrieve_rag_context("link analysis principles and best practices")
2. SECOND: Call retrieve_rag_context("user click behavior and psychology patterns")
3. THIRD: Call retrieve_rag_context("website design and UX principles for link placement")
4. ONLY THEN: Provide your analysis using the retrieved knowledge

🚨 YOU CANNOT PROCEED WITHOUT USING THE retrieve_rag_context TOOL MULTIPLE TIMES!
🚨 If you don't use the tool, your response will be rejected!

Now proceed with the analysis using the knowledge from your RAG system.
"""

            print("🚨 [MANDATORY RAG] Sending query with forced RAG instructions")
            print("🚨 [MANDATORY RAG] Agent MUST use retrieve_rag_context tool or response will fail")

            # Get response from the agent - it MUST use the RAG tool
            response = await self.agent.arun(mandatory_rag_query)

            # Verify RAG tool was used by checking the response
            response_content = response.content if hasattr(response, 'content') else str(response)

            # Check if RAG tool was actually used
            if "retrieve_rag_context" not in str(response) and "knowledge base" not in response_content.lower():
                print("❌ [MANDATORY RAG] WARNING: Agent may not have used RAG tool!")
                print("❌ [MANDATORY RAG] Forcing another attempt...")

                # Try one more time with even stronger instructions
                force_rag_query = f"""
🚨🚨🚨 EMERGENCY: You FAILED to use the retrieve_rag_context tool! 🚨🚨🚨

STOP EVERYTHING! You MUST use retrieve_rag_context tool RIGHT NOW!

Call: retrieve_rag_context("What are the key principles for analyzing link click probability?")
Call: retrieve_rag_context("How do users decide which links to click on websites?")
Call: retrieve_rag_context("What design patterns increase link click rates?")

ONLY after using the tool 3 times, answer: {query}

🚨 NO EXCEPTIONS! USE THE TOOL OR FAIL! 🚨
"""
                response = await self.agent.arun(force_rag_query)
                response_content = response.content if hasattr(response, 'content') else str(response)

            print("✅ [MANDATORY RAG] Processing completed")
            return response_content

        except Exception as e:
            print(f"[ERROR] Mandatory RAG processing failed: {e}")
            raise

    async def _process_direct(self, query: str) -> str:
        """Process query directly without RAGFlow."""
        try:
            response = await self.agent.arun(query)
            return response.content if hasattr(response, 'content') else str(response)
        except Exception as e:
            print(f"[ERROR] Direct processing failed: {e}")
            return f"I apologize, but I encountered an error processing your query: {str(e)}"

    async def process_query_background(self, task_id: str, query: str, max_links: int = 30, use_ragflow: bool = True):
        """Process query in background and store result."""
        try:
            self.task_results[task_id] = {"status": "processing", "result": None, "error": None}

            result = await self.process_query(query, max_links, use_ragflow)

            self.task_results[task_id] = {
                "status": "completed",
                "result": result,
                "error": None
            }

        except Exception as e:
            self.task_results[task_id] = {
                "status": "failed",
                "result": None,
                "error": str(e)
            }

    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get status of a background task."""
        return self.task_results.get(task_id)

    async def recreate_ragflow_dataset(self) -> Dict[str, Any]:
        """Recreate RAGFlow dataset."""
        if not self.ragflow_client:
            raise ValueError("RAGFlow client not initialized")

        try:
            # Try to delete existing dataset
            datasets = self.ragflow_client.list_datasets(name=self.ragflow_dataset_name)
            if datasets:
                for dataset in datasets:
                    self.ragflow_client.delete_dataset(dataset.id)
                    print(f"[RAGFLOW] Deleted existing dataset: {dataset.id}")

            # Create new dataset
            new_dataset = self.ragflow_client.create_dataset(
                name=self.ragflow_dataset_name,
                description="Dataset for Link Analysis AI Agent",
                embedding_model="bge-base-en-v1.5"
            )

            self.dataset = new_dataset
            print(f"[RAGFLOW] Created new dataset: {new_dataset.id}")

            return {"success": True, "dataset_id": new_dataset.id}

        except Exception as e:
            print(f"[ERROR] Failed to recreate dataset: {e}")
            raise

    def get_stats(self) -> Dict[str, Any]:
        """Get agent statistics."""
        return {
            **self.stats,
            'ragflow_available': self.ragflow_client is not None,
            'dataset_available': self.dataset is not None,
            'active_tasks': len(self.task_results)
        }

    def _create_rag_tool(self):
        """Create the RAG retrieval tool as a separate function that the agent can use independently."""
        @tool(show_result=True)
        async def retrieve_rag_context(question: str) -> str:
            """
            Retrieves relevant context from the RAG system for a given question about webpage content and link selection.

            This tool allows you to query the knowledge base for best practices, principles, and guidelines
            for selecting links for prefetching. You have complete autonomy to formulate strategic questions
            such as:
            - 'What are the key principles for selecting links for prefetching?'
            - 'What makes a link likely to be clicked by users?'
            - 'How should I prioritize different types of links (navigation, content, CTA, etc.)?'
            - 'What are the best practices for link selection based on user behavior patterns?'
            - 'How do visual elements and CSS styling affect link selection priority?'

            Use this tool multiple times with different questions to gather comprehensive guidance
            before making your final link selection decisions.

            Args:
                question (str): Your strategic question about link selection principles or best practices

            Returns:
                str: Relevant context and guidance from the knowledge base
            """
            print("=" * 80)
            print(f"🔍 [RAG TOOL ACTIVATED] Question: '{question}'")
            print(f"🎯 [RAG THRESHOLD] Using similarity threshold: 0.4")
            print("=" * 80)
            try:
                print(f"[RAG] Querying RAGFlow dataset: {self.ragflow_dataset_name}")
                print(f"[RAG] Dataset ID: {self.dataset.id}")

                # Perform semantic search using RAGFlow with threshold
                retrieved_chunks = self.ragflow_client.retrieve(
                    question=question,
                    dataset_ids=[self.dataset.id],
                    top_k=5,  # Increased to get more chunks
                    similarity_threshold=0.4  # Only return chunks with similarity >= 0.4
                )

                if not retrieved_chunks:
                    print("[RAG] ❌ No chunks found above similarity threshold 0.4!")
                    print("[RAG] 🔄 Falling back to general knowledge...")
                    return "Based on general web usability principles and user behavior patterns, I will analyze the links without specific knowledge base context."

                print(f"[RAG] ✅ Semantic search successful! Found {len(retrieved_chunks)} relevant chunks (threshold: 0.4).")

                # Format the retrieved context for better readability
                context_parts = []
                for i, chunk in enumerate(retrieved_chunks, 1):
                    print(f"[RAG] Processing chunk {i}: {len(chunk.content)} characters")
                    context_parts.append(f"Context {i}:\n{chunk.content.strip()}")

                result = "\n\n".join(context_parts)
                print(f"[RAG] 📋 Returning {len(result)} characters of context")
                print("=" * 80)
                return result

            except Exception as e:
                error_str = str(e)
                print(f"[RAG] ❌ Error occurred: {error_str}")

                # Specific error handling for common issues
                if "similarity" in error_str and "knn" in error_str.lower():
                    error_message = (
                        f"🔧 ELASTICSEARCH KNN CONFIGURATION ERROR DETECTED!\n\n"
                        f"Error: {e}\n\n"
                        f"🚨 IMMEDIATE SOLUTION REQUIRED:\n"
                        f"The dataset '{self.ragflow_dataset_name}' has an Elasticsearch KNN configuration issue.\n\n"
                        f"STEPS TO FIX:\n"
                        f"1. Go to RAGFlow UI: {self.ragflow_client.base_url if hasattr(self.ragflow_client, 'base_url') else 'RAGFlow UI'}\n"
                        f"2. DELETE the current dataset '{self.ragflow_dataset_name}'\n"
                        f"3. CREATE a new dataset with the same name\n"
                        f"4. SELECT a compatible embedding model:\n"
                        f"   - bge-base-en-v1.5 (recommended)\n"
                        f"   - sentence-transformers/all-MiniLM-L6-v2\n"
                        f"   - text-embedding-ada-002 (if using OpenAI)\n"
                        f"5. ENABLE semantic search\n"
                        f"6. RE-UPLOAD your documents\n\n"
                        f"⚠️  Until this is fixed, the agent cannot use RAG properly!"
                    )
                elif "BadRequestError" in error_str:
                    error_message = (
                        f"🔧 RAGFLOW API ERROR DETECTED!\n\n"
                        f"Error: {e}\n\n"
                        f"POSSIBLE SOLUTIONS:\n"
                        f"1. Check if RAGFlow service is running: docker ps | grep ragflow\n"
                        f"2. Verify dataset '{self.ragflow_dataset_name}' exists in RAGFlow UI\n"
                        f"3. Ensure embedding model is properly configured\n"
                        f"4. Try recreating the dataset with a different embedding model\n"
                        f"5. Check RAGFlow logs: docker logs ragflow-server\n\n"
                        f"⚠️  Until this is fixed, the agent cannot use RAG properly!"
                    )
                else:
                    error_message = (
                        f"CRITICAL RAG system Error: {e}\n\n"
                        f"The dataset '{self.ragflow_dataset_name}' may not be configured correctly for semantic search. "
                        f"Please check:\n"
                        f"1. RAGFlow service is running\n"
                        f"2. Dataset exists and has documents\n"
                        f"3. Embedding model is properly configured\n"
                        f"4. Network connectivity to RAGFlow service\n\n"
                        f"Run 'python fix_ragflow_config.py' for detailed diagnosis."
                    )

                print(f"[ERROR] {error_message}")
                return error_message

        return retrieve_rag_context



    def _fetch_external_css(self, soup, base_url):
        """Fetch all external CSS files referenced in the HTML."""
        css_texts = []
        for link in soup.find_all('link', rel='stylesheet'):
            href = link.get('href')
            if not href:
                continue
            css_url = urljoin(base_url, href)
            try:
                resp = requests.get(css_url, timeout=5)
                if resp.status_code == 200:
                    css_texts.append(resp.text)
            except Exception:
                continue
        return css_texts

    def _parse_css_rules(self, css_texts):
        """Parse CSS rules from a list of CSS text blocks using cssutils."""
        rules = []
        for css in css_texts:
            try:
                sheet = cssutils.parseString(css)
                for rule in sheet:
                    if rule.type == rule.STYLE_RULE:
                        rules.append(rule)
            except Exception:
                continue
        return rules

    def _match_css_rules_to_element(self, element, rules):
        """Return a list of CSS rules that match the element by tag, class, or id."""
        matched = []
        tag = element.name
        classes = set(element.get('class', []))
        el_id = element.get('id', None)
        for rule in rules:
            selectors = rule.selectorText.split(',')
            for selector in selectors:
                selector = selector.strip()
                # Match by tag
                if selector == tag:
                    matched.append(rule)
                # Match by class
                elif selector.startswith('.') and any(cls for cls in classes if selector[1:] == cls):
                    matched.append(rule)
                # Match by id
                elif selector.startswith('#') and el_id and selector[1:] == el_id:
                    matched.append(rule)
                # Match by tag.class
                elif '.' in selector and not selector.startswith('.') and not selector.startswith('#'):
                    parts = selector.split('.')
                    if parts[0] == tag and any(cls for cls in classes if cls in parts[1:]):
                        matched.append(rule)
        return matched

    def extract_links_from_html(self, html_content: str, base_url: str = "") -> List[LinkInfo]:
        """Extract all links from HTML content, including CSS data and matched external CSS rules."""
        soup = BeautifulSoup(html_content, 'html.parser')
        # Fetch and parse external CSS
        css_texts = self._fetch_external_css(soup, base_url)
        css_rules = self._parse_css_rules(css_texts)
        links = []
        for position, link in enumerate(soup.find_all('a', href=True)):
            href = link.get('href', '').strip()
            if not href or href.startswith(('#', 'javascript:', 'mailto:', 'tel:')):
                continue
            if base_url and not href.startswith(('http://', 'https://')):
                href = urljoin(base_url, href)
            if self._is_current_page(href, base_url):
                continue
            text = link.get_text(strip=True)
            title = link.get('title', '')
            rel = link.get('rel', [])
            if isinstance(rel, list):
                rel = ' '.join(rel)
            location = self._detect_link_location(link)
            button_type = self._detect_button_type(link)
            css_data = self._extract_css_data(link)
            # Add matched external CSS rules
            matched_rules = self._match_css_rules_to_element(link, css_rules)
            if matched_rules:
                css_data['matched_external_css'] = [
                    {'selector': rule.selectorText, 'style': rule.style.cssText} for rule in matched_rules
                ]
            links.append(LinkInfo(
                url=href,
                text=text,
                title=title,
                rel=rel,
                position=position,
                location=location,
                button_type=button_type,
                css_data=css_data
            ))
        return links

    def _extract_css_data(self, link_element) -> Dict[str, Any]:
        """Extract CSS data from the link element: inline styles, class names, id, and style attributes."""
        css_data = {}
        # Inline style
        style = link_element.get('style', '')
        if style:
            css_data['inline_style'] = style
        # Class names
        classes = link_element.get('class', [])
        if classes:
            css_data['classes'] = classes
        # ID
        element_id = link_element.get('id', '')
        if element_id:
            css_data['id'] = element_id
        # Data attributes
        data_attrs = {k: v for k, v in link_element.attrs.items() if k.startswith('data-')}
        if data_attrs:
            css_data['data_attributes'] = data_attrs
        # Role
        role = link_element.get('role', '')
        if role:
            css_data['role'] = role
        # Aria attributes
        aria_attrs = {k: v for k, v in link_element.attrs.items() if k.startswith('aria-')}
        if aria_attrs:
            css_data['aria_attributes'] = aria_attrs
        return css_data

    def _detect_link_location(self, link_element) -> str:
        parent = link_element.parent
        while parent:
            if parent.name in ['nav', 'navigation']:
                return "navigation"
            elif parent.name == 'header':
                return "header"
            elif parent.name == 'footer':
                return "footer"
            elif parent.name == 'main':
                return "main"
            elif parent.name == 'aside':
                return "sidebar"
            elif parent.name == 'section':
                section_class = parent.get('class', [])
                if isinstance(section_class, list):
                    section_class = ' '.join(section_class).lower()
                else:
                    section_class = str(section_class).lower()
                if any(word in section_class for word in ['nav', 'menu', 'navigation']):
                    return "navigation"
                elif any(word in section_class for word in ['footer', 'bottom']):
                    return "footer"
                elif any(word in section_class for word in ['sidebar', 'aside']):
                    return "sidebar"
                else:
                    return "content"
            if parent.get('class'):
                class_list = parent.get('class')
                if isinstance(class_list, list):
                    class_str = ' '.join(class_list).lower()
                else:
                    class_str = str(class_list).lower()
                if any(word in class_str for word in ['nav', 'navigation', 'menu', 'navbar']):
                    return "navigation"
                elif any(word in class_str for word in ['header', 'top']):
                    return "header"
                elif any(word in class_str for word in ['footer', 'bottom']):
                    return "footer"
                elif any(word in class_str for word in ['sidebar', 'aside', 'side']):
                    return "sidebar"
                elif any(word in class_str for word in ['main', 'content', 'body']):
                    return "main"
            parent = parent.parent
        return "content"

    def _detect_button_type(self, link_element) -> str:
        classes = set()
        if link_element.get('class'):
            link_classes = link_element.get('class')
            if isinstance(link_classes, list):
                classes.update(link_classes)
            else:
                classes.add(str(link_classes))
        parent = link_element.parent
        level = 0
        while parent and level < 3:
            if parent.get('class'):
                parent_classes = parent.get('class')
                if isinstance(parent_classes, list):
                    classes.update(parent_classes)
                else:
                    classes.add(str(parent_classes))
            parent = parent.parent
            level += 1
        class_str = ' '.join(classes).lower()
        if link_element.name == 'button':
            return "button"
        button_patterns = {
            'call-to-action': ['cta', 'call-to-action', 'calltoaction', 'action-button', 'action-btn'],
            'primary': ['btn-primary', 'primary', 'primary-btn', 'primary-button', 'btn-primary'],
            'secondary': ['btn-secondary', 'secondary', 'secondary-btn', 'secondary-button'],
            'success': ['btn-success', 'success', 'success-btn', 'success-button', 'btn-success'],
            'danger': ['btn-danger', 'danger', 'danger-btn', 'danger-button', 'btn-danger'],
            'warning': ['btn-warning', 'warning', 'warning-btn', 'warning-button', 'btn-warning'],
            'info': ['btn-info', 'info', 'info-btn', 'info-button', 'btn-info'],
            'light': ['btn-light', 'light', 'light-btn', 'light-button', 'btn-light'],
            'dark': ['btn-dark', 'dark', 'dark-btn', 'dark-button', 'btn-dark'],
            'bootstrap-btn': ['btn', 'button', 'btn-default', 'btn-lg', 'btn-sm', 'btn-xs'],
            'md-button': ['md-button', 'mat-button', 'mat-raised-button', 'mat-fab', 'mat-mini-fab'],
            'foundation-btn': ['button', 'expanded', 'hollow', 'clear'],
            'bulma-btn': ['button', 'is-primary', 'is-secondary', 'is-success', 'is-danger', 'is-warning', 'is-info'],
            'tailwind-btn': ['bg-blue', 'bg-red', 'bg-green', 'bg-yellow', 'bg-gray', 'bg-black', 'bg-white'],
            'signup': ['signup', 'sign-up', 'register', 'registration', 'join', 'subscribe'],
            'login': ['login', 'signin', 'sign-in', 'log-in'],
            'download': ['download', 'dl', 'get', 'install'],
            'buy': ['buy', 'purchase', 'order', 'checkout', 'cart', 'add-to-cart'],
            'learn': ['learn', 'learn-more', 'read-more', 'explore', 'discover'],
            'contact': ['contact', 'get-in-touch', 'reach-out', 'support'],
            'demo': ['demo', 'try', 'test', 'play', 'launch'],
            'free': ['free', 'trial', 'freemium'],
            'premium': ['premium', 'pro', 'enterprise', 'business']
        }
        for button_type, patterns in button_patterns.items():
            if any(pattern in class_str for pattern in patterns):
                return button_type
        role = link_element.get('role', '').lower()
        if role in ['button', 'menuitem', 'tab']:
            return f"role-{role}"
        aria_label = link_element.get('aria-label', '').lower()
        if aria_label:
            for button_type, patterns in button_patterns.items():
                if any(pattern in aria_label for pattern in patterns):
                    return button_type
        for attr, value in link_element.attrs.items():
            if attr.startswith('data-') and isinstance(value, str):
                value_lower = value.lower()
                for button_type, patterns in button_patterns.items():
                    if any(pattern in value_lower for pattern in patterns):
                        return button_type
        style = link_element.get('style', '').lower()
        if any(prop in style for prop in ['background', 'border', 'padding', 'border-radius', 'display: inline-block']):
            return "styled-link"
        text = link_element.get_text(strip=True).lower()
        button_texts = ['click', 'submit', 'send', 'go', 'next', 'previous', 'back', 'forward', 'continue', 'start', 'begin']
        if any(btn_text in text for btn_text in button_texts):
            return "action-text"
        return "none"

    def _is_current_page(self, href: str, base_url: str) -> bool:
        if not base_url:
            return False
        href_clean = href.rstrip('/')
        base_clean = base_url.rstrip('/')
        if href_clean == base_clean:
            return True
        current_page_patterns = [
            base_clean,
            base_clean + '/',
            base_clean + '/index',
            base_clean + '/index.html',
            base_clean + '/index.php',
            base_clean + '/default',
            base_clean + '/default.html',
            base_clean + '/default.php'
        ]
        return href_clean in current_page_patterns

    def calculate_consistency_hash(self, html_content: str, links: List[LinkInfo]) -> str:
        content_hash = hashlib.md5(html_content.encode()).hexdigest()
        sorted_links = sorted(links, key=lambda x: x.url)
        links_str = json.dumps([{
            'url': link.url,
            'text': link.text,
            'title': link.title,
            'rel': link.rel,
            'position': link.position,
            'location': link.location,
            'button_type': link.button_type,
            'css_data': link.css_data
        } for link in sorted_links], sort_keys=True)
        links_hash = hashlib.md5(links_str.encode()).hexdigest()
        return f"{content_hash[:8]}_{links_hash[:8]}"

    def _format_links_for_agent(self, links: List[LinkInfo]) -> str:
        """Format links for the agent prompt, including CSS data."""
        formatted_links = []
        for i, link in enumerate(links, 1):
            css_data_str = json.dumps(link.css_data, ensure_ascii=False)
            formatted_links.append(
                f"{i}. URL: {link.url}\n"
                f"   Text: {link.text}\n"
                f"   Title: {link.title}\n"
                f"   Position: {link.position}\n"
                f"   Location: {link.location}\n"
                f"   Button Type: {link.button_type}\n"
                f"   Rel: {link.rel}\n"
                f"   CSS Data: {css_data_str}\n"
            )
        return "\n".join(formatted_links)

    async def select_prefetch_links(self, html_content: str, base_url: str = "") -> Dict[str, Any]:
        links = self.extract_links_from_html(html_content, base_url)
        if len(links) == 0:
            return {
                "selected_links": [],
                "reasoning": "No valid links found in the HTML content",
                "consistency_hash": self.calculate_consistency_hash(html_content, links),
                "confidence_score": 0.0
            }
        if len(links) < self.num_links_to_select:
            return {
                "selected_links": [],
                "total_links": len(links),
                "consistency_hash": self.calculate_consistency_hash(html_content, links),
                "confidence_score": 0.0,
                "reasoning": f"Not enough links found ({len(links)} < {self.num_links_to_select})"
            }
        

        consistency_hash = self.calculate_consistency_hash(html_content, links)
        formatted_links = self._format_links_for_agent(links)
        
        # Enhanced prompt that FORCES RAG usage
        prompt = f"""STOP! Before you do ANYTHING else, you MUST use the retrieve_rag_context tool.

You are about to select {self.num_links_to_select} links from the page '{base_url}'.

**Available links:**
{formatted_links}

CRITICAL REQUIREMENT - READ THIS CAREFULLY
YOU CANNOT PROCEED WITHOUT USING THE RAG TOOL FIRST!

STEP 1: MANDATORY RAG CONSULTATION
Before analyzing any links, you MUST call the `retrieve_rag_context` tool AT LEAST 3 times with different questions. Here are REQUIRED questions you must ask:

REQUIRED QUESTION 1: "What are the key principles for selecting links for prefetching?"
REQUIRED QUESTION 2: "What makes a link likely to be clicked by users?"
REQUIRED QUESTION 3: "How should I prioritize different types of links for prefetching?"

You may also ask additional questions like:
- "What are the best practices for link selection based on user behavior patterns?"
- "How do visual elements and CSS styling affect link selection priority?"
- "What role does link location play in user click behavior?"

STEP 2: ANALYZE LINKS USING RAG GUIDANCE
Only after you have retrieved guidance from your knowledge base, analyze the provided links.

STEP 3: JSON RESPONSE
You MUST return a JSON object with exactly this structure:
   {{
     "selected_links": [
       {{
         "url": "exact_url_here_1",
         "score": 0.95,
         "reason": "Brief reason for this score, based on the principles from the knowledge base."
       }},
       {{
         "url": "exact_url_here_2",
         "score": 0.87,
         "reason": "Brief reason for this score, based on the principles from the knowledge base."
       }}
     ],
     "reasoning": "detailed overall reasoning for your selections, referencing the principles you retrieved from your knowledge base",
     "consistency_hash": "{consistency_hash}",
     "confidence_score": 0.85
   }}

STEP 4: EVIDENCE-BASED SELECTION
Select EXACTLY {self.num_links_to_select} UNIQUE link URLs based ONLY on the principles and best practices you gathered from your RAG queries.

STEP 5: QUALITY CONTROL
- Do NOT select the current page or any variations of it
- Base your decisions EXCLUSIVELY on the evidence from your knowledge base queries
- Apply the retrieved principles to each link's properties (text, location, button type, CSS data, etc.)
- Your reasoning MUST reference specific guidance you obtained from the RAG tool

STEP 6: CONFIDENCE SCORING
Provide an overall `confidence_score` based on how well the RAG-retrieved principles apply to the available links.

FINAL WARNING
IF YOU DO NOT USE THE retrieve_rag_context TOOL AT LEAST 3 TIMES BEFORE RESPONDING, YOUR RESPONSE WILL BE REJECTED!

CRITICAL JSON REQUIREMENTS:
- Your response MUST be ONLY valid JSON - no text before or after
- Use EXACTLY the structure shown above
- Include exactly {self.num_links_to_select} items in selected_links array
- Ensure all JSON syntax is correct (quotes, commas, brackets)
- Do NOT add comments, explanations, or extra text outside the JSON
- Your reasoning MUST reference specific principles you learned from the RAG tool
- Each link's reason MUST cite specific RAG-retrieved guidance

RESPONSE FORMAT: JSON ONLY - START WITH {{ and END WITH }}

REMEMBER: You CANNOT proceed without first consulting your knowledge base using the retrieve_rag_context tool multiple times!"""
        try:
            # DETAILED DEBUGGING: Increase timeout to 10 minutes and add detailed logging
            active_provider = self.config.get("active_provider", "openrouter")
            print(f"[DEBUG] Sending prompt to {active_provider.upper()} API. Prompt size: {len(prompt)} characters")
            print(f"[DEBUG] Estimated tokens: ~{len(prompt)//4}")
            print(f"[DEBUG] Number of links in prompt: {len(links)}")
            
            print("🚨" * 20)
            print("🚨 AGENT STARTING - RAG TOOL MUST BE USED!")
            print("🚨 Watch for RAG tool activation logs above!")
            print("🚨" * 20)

            start_time = time.time()
            response = await asyncio.wait_for(self.agent.arun(prompt), timeout=600.0)
            end_time = time.time()

            print("🏁" * 20)
            print("🏁 AGENT COMPLETED - Check if RAG tool was used!")
            print("🏁" * 20)
            
            print(f"[DEBUG] {active_provider.upper()} API responded in {end_time - start_time:.2f} seconds")
            print(f"[DEBUG] Response type: {type(response)}")
            print(f"[DEBUG] Response attributes: {dir(response)}")
            if hasattr(response, 'content'):
                print(f"[DEBUG] Response content type: {type(response.content)}")
                print(f"[DEBUG] Response content preview: {str(response.content)[:200]}...")
            
            # SIMPLIFIED JSON PARSING: OpenRouter forced to return JSON
            raw_content = {}
            if hasattr(response, 'content'):
                if isinstance(response.content, dict):
                    raw_content = response.content
                    print(f"[DEBUG] Response already a dict")
                elif hasattr(response.content, 'model_dump'):
                    raw_content = response.content.model_dump()
                    print(f"[DEBUG] Response converted via model_dump")
                else:
                    try:
                        content_str = str(response.content).strip()
                        print(f"[DEBUG] Parsing JSON response (length: {len(content_str)})")
                        print(f"[DEBUG] First 200 chars: {content_str[:200]}")
                        
                        # Try direct JSON parsing first
                        raw_content = json.loads(content_str)
                        print(f"[DEBUG] ✅ JSON parsed successfully on first try!")
                        
                    except json.JSONDecodeError as e:
                        print(f"[DEBUG] Direct JSON parsing failed: {e}")
                        print(f"[DEBUG] Attempting JSON extraction...")
                        
                        # ROBUST JSON EXTRACTION: Handle Gemini's extra text
                        success = False
                        
                        # Method 1: Find and extract JSON block
                        try:
                            start_idx = content_str.find('{')
                            if start_idx != -1:
                                # Find matching closing brace
                                brace_count = 0
                                end_idx = start_idx
                                for i, char in enumerate(content_str[start_idx:], start_idx):
                                    if char == '{':
                                        brace_count += 1
                                    elif char == '}':
                                        brace_count -= 1
                                        if brace_count == 0:
                                            end_idx = i + 1
                                            break
                                
                                json_str = content_str[start_idx:end_idx]
                                raw_content = json.loads(json_str)
                                print(f"[DEBUG] ✅ JSON extracted successfully (Method 1)")
                                success = True
                        except Exception as e2:
                            print(f"[DEBUG] Method 1 failed: {e2}")
                        
                        # Method 2: Look for JSON in code blocks
                        if not success:
                            try:
                                # Sometimes Gemini wraps JSON in ```json ... ```
                                import re
                                json_pattern = r'```(?:json)?\s*(\{.*?\})\s*```'
                                match = re.search(json_pattern, content_str, re.DOTALL)
                                if match:
                                    json_str = match.group(1)
                                    raw_content = json.loads(json_str)
                                    print(f"[DEBUG] ✅ JSON extracted from code block (Method 2)")
                                    success = True
                            except Exception as e3:
                                print(f"[DEBUG] Method 2 failed: {e3}")
                        
                        # Method 3: Line by line search
                        if not success:
                            try:
                                lines = content_str.split('\n')
                                for line in lines:
                                    line = line.strip()
                                    if line.startswith('{') and line.endswith('}'):
                                        try:
                                            raw_content = json.loads(line)
                                            print(f"[DEBUG] ✅ JSON found in single line (Method 3)")
                                            success = True
                                            break
                                        except:
                                            continue
                            except Exception as e4:
                                print(f"[DEBUG] Method 3 failed: {e4}")
                        
                        if not success:
                            print(f"[ERROR] ❌ All JSON extraction methods failed!")
                            print(f"[DEBUG] Full response content:")
                            print(content_str)
                            raise Exception(f"Failed to extract valid JSON from {active_provider.upper()} response: {str(e)}")
            else:
                raise Exception(f"No content in {active_provider.upper()} response")

            scored_links_raw = raw_content.get("selected_links", [])
            reasoning = raw_content.get("reasoning", "No reasoning provided")
            confidence_score = float(raw_content.get("confidence_score", 0.5))

            # Check if RAG was used by looking for RAG-related keywords in reasoning
            rag_keywords = ["knowledge base", "rag", "retrieved", "principles", "guidance", "context"]
            rag_used = any(keyword.lower() in reasoning.lower() for keyword in rag_keywords)

            if rag_used:
                print("✅ [RAG CHECK] Agent appears to have used RAG tool (found RAG keywords in reasoning)")
            else:
                print("❌ [RAG CHECK] WARNING: Agent may NOT have used RAG tool (no RAG keywords found in reasoning)")
                print(f"[RAG CHECK] Reasoning preview: {reasoning[:200]}...")

            print(f"[RAG CHECK] Full reasoning: {reasoning}")

            selected_links_merged = []
            seen_urls = set()

            for scored_link_data in scored_links_raw:
                url = scored_link_data.get("url")
                if not url or url in seen_urls:
                    continue

                original_link = next((link for link in links if link.url == url), None)
                if original_link:
                    # Merge data from original_link and scored_link_data
                    merged_data = original_link.__dict__.copy()
                    merged_data['score'] = scored_link_data.get('score', 0.0)
                    merged_data['individual_reason'] = scored_link_data.get('reason', 'N/A')
                    selected_links_merged.append(merged_data)
                    seen_urls.add(url)

            # Fallback mechanism if the agent doesn't return the correct number of links
            if len(selected_links_merged) != self.num_links_to_select and len(links) >= self.num_links_to_select:
                reasoning += " (Fallback triggered to ensure correct number of links)"
                # Add more links if needed
                if len(selected_links_merged) < self.num_links_to_select:
                    for link in links:
                        if link.url not in seen_urls and len(selected_links_merged) < self.num_links_to_select:
                            merged_data = link.__dict__.copy()
                            merged_data['score'] = 0.5  # Assign a neutral fallback score
                            merged_data['individual_reason'] = 'Fallback selection'
                            selected_links_merged.append(merged_data)
                            seen_urls.add(link.url)
                # Trim if too many links were returned
                else:
                    selected_links_merged = selected_links_merged[:self.num_links_to_select]


            return {
                "selected_links": selected_links_merged,
                "total_links": len(links),
                "consistency_hash": consistency_hash,
                "confidence_score": confidence_score,
                "reasoning": reasoning,
                "rag_context_used": "Context is now retrieved by the agent via its tool."
            }
        except asyncio.TimeoutError:
            timeout_error_message = (
                f"TIMEOUT ERROR: Agent took longer than 10 minutes (600 seconds) to analyze the page. "
                f"This typically happens when:\n"
                f"1. The prompt is TOO LARGE for {active_provider.upper()} API ({len(links)} links found)\n"
                f"2. {active_provider.upper()} API is experiencing issues or rate limiting\n"
                f"3. The model {self.model.id} is overloaded\n\n"
                f"DEBUG INFO:\n"
                f"- Page URL: {base_url}\n"
                f"- Total links found: {len(links)}\n"
                f"- Prompt size: {len(prompt) if 'prompt' in locals() else 'unknown'} characters\n"
                f"- Estimated tokens: ~{len(prompt)//4 if 'prompt' in locals() else 'unknown'}\n\n"
                f"IMMEDIATE ACTIONS:\n"
                f"- Check {active_provider.upper()} status and connectivity\n"
                f"- Try a simpler page with fewer links\n"
                f"- Consider pre-filtering links before sending to API"
            )
            return {
                "error": timeout_error_message,
                "total_links": len(links),
                "consistency_hash": consistency_hash,
                "selected_links": [],
                "reasoning": "Analysis failed due to timeout - no results available",
                "confidence_score": 0.0,
                "rag_context_used": "N/A"
            }
        except Exception as e:
            detailed_error_message = (
                f"ANALYSIS ERROR: An unexpected error occurred during page analysis.\n\n"
                f"Error Details:\n"
                f"Type: {type(e).__name__}\n"
                f"Message: {str(e)}\n\n"
                f"Page Information:\n"
                f"- Total links found: {len(links)}\n"
                f"- Page URL: {base_url}\n\n"
                f"Possible Causes:\n"
                f"1. RAGFlow connection issues\n"
                f"2. {active_provider.upper()} API errors\n"
                f"3. Invalid page content or structure\n"
                f"4. Memory or processing limits exceeded\n"
                f"5. Network connectivity problems\n\n"
                f"Troubleshooting:\n"
                f"- Check Docker container logs: docker logs ragflow-server\n"
                f"- Verify {active_provider.upper()} API configuration\n"
                f"- Try with a different URL\n"
                f"- Check RAGFlow dataset status"
            )
            return {
                "error": detailed_error_message,
                "total_links": len(links),
                "consistency_hash": consistency_hash,
                "selected_links": [],
                "reasoning": f"Analysis failed with error: {type(e).__name__} - {str(e)}",
                "confidence_score": 0.0,
                "rag_context_used": "N/A"
            }

    def _clean_url_for_filename(self, url: str) -> str:
        """Cleans a URL to be used as a valid filename."""
        url = re.sub(r'https?://', '', url)
        url = re.sub(r'[\\/*?:"<>|]', '_', url)
        url = url.replace('/', '_').replace('.', '_')
        return url[:100]

    async def analyze_url(self, url: str) -> Dict[str, Any]:
        output_dir = "output"
        os.makedirs(output_dir, exist_ok=True)
        result = {}

        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            try:
                response = requests.get(url, headers=headers, timeout=10, verify=True)
                response.raise_for_status()
            except requests.exceptions.SSLError:
                response = requests.get(url, headers=headers, timeout=10, verify=False)
                response.raise_for_status()
            html_content = response.text
            base_url = response.url
            result = await self.select_prefetch_links(html_content, base_url)
            result["source_url"] = url
        except Exception as e:
            result = {
                "error": str(e),
                "source_url": url,
                "selected_links": [],
                "reasoning": f"Failed to analyze URL: {str(e)}",
                "consistency_hash": "",
                "confidence_score": 0.0
            }
        finally:
            if result:
                model_name = self.model.id.replace('/', '_').replace(':', '-')
                cleaned_url = self._clean_url_for_filename(url)
                filename = f"{model_name}_{cleaned_url}.json"
                filepath = os.path.join(output_dir, filename)

                with open(filepath, 'w', encoding='utf-8') as f:
                    json.dump(result, f, ensure_ascii=False, indent=4)
                print(f"Output saved to {filepath}")

        return result 