events {
    worker_connections 1024;
}

http {
    upstream ragflow {
        server ragflow-server:9380;
    }
    
    upstream bilge-kagan {
        server bilge-kagan-app:8000;
    }

    server {
        listen 80;
        server_name localhost;

        # RAGFlow Web Interface - direct proxy without rewrite
        location /ragflow/ {
            proxy_pass http://ragflow-server:80/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Prefix /ragflow;
        }

        # Ollama API
        location /ollama/ {
            proxy_pass http://ollama:11434/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # Remove /ollama prefix
            rewrite ^/ollama/(.*)$ /$1 break;
        }

        # RAGFlow API
        location /v1/ {
            proxy_pass http://ragflow/v1/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Serve static web interface
        location / {
            root /usr/share/nginx/html;
            index index.html;
            try_files $uri $uri/ /index.html;
        }

        # Bilge Kağan API endpoints
        location /api/ {
            proxy_pass http://bilge-kagan/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # Remove /api prefix
            rewrite ^/api/(.*)$ /$1 break;
        }

        # Direct API endpoints
        location ~ ^/(health|config|query|stats)$ {
            proxy_pass http://bilge-kagan$request_uri;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Task endpoint with ID parameter
        location ~ ^/task/(.+)$ {
            proxy_pass http://bilge-kagan$request_uri;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Static files and assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            proxy_pass http://ragflow;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
}
