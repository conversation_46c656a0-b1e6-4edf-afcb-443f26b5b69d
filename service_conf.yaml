# RAGFlow Service Configuration

# Database Configuration
mysql:
  name: rag_flow
  user: ragflow
  password: infiniflow
  host: mysql
  port: 3306

# Redis Configuration  
redis:
  host: redis
  port: 6379
  password: ""

# Elasticsearch Configuration
elasticsearch:
  hosts: ["es01:9200"]
  
# MinIO Configuration
minio:
  host: minio
  port: 9000
  user: ragflow
  password: infiniflow
  secure: false

# Server Configuration
server:
  host: 0.0.0.0
  port: 9380

# Default LLM Configuration
user_default_llm:
  "OpenAI-API-Compatible":
    "api_key": "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"
    "base_url": "https://openrouter.ai/api/v1"
    "model_name": "google/gemini-2.5-pro"

# Embedding Model Configuration
embedding:
  default_model: "bge-base-en-v1.5"
