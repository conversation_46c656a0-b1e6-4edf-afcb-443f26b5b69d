services:
  # Elasticsearch
  es01:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: es01
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms1g -Xmx1g"
      - bootstrap.memory_lock=true
      - xpack.security.enabled=false
      - xpack.security.http.ssl.enabled=false
      - xpack.security.transport.ssl.enabled=false
    ulimits:
      memlock:
        soft: -1
        hard: -1
    volumes:
      - esdata:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - ragflow-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Redis
  redis:
    image: redis:7.2-alpine
    container_name: redis
    command: redis-server --appendonly yes --requirepass infini_rag_flow
    environment:
      TZ: ${TIMEZONE}
    volumes:
      - redisdata:/data
    ports:
      - "${REDIS_PORT}:6379"
    networks:
      - ragflow-network
    healthcheck:
      test: ["<PERSON><PERSON>", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5

  # MySQL
  mysql:
    image: mysql:8.0
    container_name: mysql
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: ${MYSQL_DATABASE}
      MYSQL_USER: ${MYSQL_USER}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
      TZ: ${TIMEZONE}
    volumes:
      - mysqldata:/var/lib/mysql
    ports:
      - "${MYSQL_PORT}:3306"
    networks:
      - ragflow-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${MYSQL_ROOT_PASSWORD}"]
      interval: 30s
      timeout: 10s
      retries: 5


  # MinIO Object Storage
  minio:
    image: minio/minio:latest
    container_name: minio
    command: server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER: rag_flow
      MINIO_ROOT_PASSWORD: infini_rag_flow
    volumes:
      - minio_data:/data
    ports:
      - "9000:9000"
      - "9001:9001"
    networks:
      - ragflow-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 10s
      retries: 5

  # RAGFlow Server
  ragflow-server:
    image: infiniflow/ragflow:v0.19.1
    container_name: ragflow-server
    depends_on:
      es01:
        condition: service_healthy
      redis:
        condition: service_healthy
      mysql:
        condition: service_healthy
      minio:
        condition: service_healthy

    environment:
      - MYSQL_PASSWORD=${MYSQL_PASSWORD}
      - MYSQL_USER=${MYSQL_USER}
      - MYSQL_DB=${MYSQL_DATABASE}
      - MYSQL_HOST=mysql
      - REDIS_HOST=redis
      - REDIS_PASSWORD=infini_rag_flow
      - ES_HOST=es01
      - MINIO_HOST=minio
      - MINIO_USER=rag_flow
      - MINIO_PASSWORD=infini_rag_flow
      - TZ=${TIMEZONE}
    volumes:
      - ragflow_logs:/ragflow/logs
      - ragflow_data:/ragflow/data
    ports:
      - "9380:9380"  # RAGFlow API
      - "8080:80"    # RAGFlow Web GUI - nginx port 80'i 8080'e yönlendir
    networks:
      - ragflow-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9380/v1/api/health"]
      interval: 30s
      timeout: 10s
      retries: 10
      start_period: 120s

  # Your Application
  bilge-kagan-app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: bilge-kagan-app
    depends_on:
      ragflow-server:
        condition: service_healthy
    environment:
      - RAGFLOW_BASE_URL=http://ragflow-server:9380
    volumes:
      - ./app:/app
      - ./logs:/app/logs
    ports:
      - "8000:8000"
      - "7869:7869"
    networks:
      - ragflow-network
    restart: unless-stopped
    command: python main.py

  # Ollama for Local LLM and Embeddings
  ollama:
    image: ollama/ollama:latest
    container_name: ollama
    ports:
      - "11435:11434"
    volumes:
      - ollama_data:/root/.ollama
    environment:
      - OLLAMA_HOST=0.0.0.0
    networks:
      - ragflow-network
    restart: unless-stopped
    # GPU support (uncomment if you have NVIDIA GPU)
    # deploy:
    #   resources:
    #     reservations:
    #       devices:
    #         - driver: nvidia
    #           count: all
    #           capabilities: [gpu]

  # Nginx Proxy for RAGFlow GUI
  nginx:
    image: nginx:alpine
    container_name: ragflow-nginx
    depends_on:
      - ragflow-server
      - ollama
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./web:/usr/share/nginx/html:ro
    networks:
      - ragflow-network
    restart: unless-stopped

volumes:
  esdata:
  redisdata:
  mysqldata:
  minio_data:
  ragflow_logs:
  ragflow_data:
  ollama_data:

networks:
  ragflow-network:
    driver: bridge
